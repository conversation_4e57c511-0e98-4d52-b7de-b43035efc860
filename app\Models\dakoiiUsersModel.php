<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Dakoii Users Model
 * 
 * Model for dakoii_users table - Dakoii portal system users
 * This is separate from the main users table which is for organization staff
 */
class DakoiiUsersModel extends Model
{
    protected $table      = 'dakoii_users';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;

    // Fields matching dakoii_users table structure
    protected $allowedFields = [
        'name',
        'username', 
        'password',
        'role',
        'is_active'
    ];

    // Enable automatic timestamps
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    // Simple validation rules for dakoii_users
    protected $validationRules = [
        'name'     => 'required|min_length[2]|max_length[255]',
        'username' => 'required|min_length[3]|max_length[255]',
        'password' => 'required|min_length[6]',
        'role'     => 'required|in_list[user,moderator,admin]',
        'is_active'=> 'in_list[0,1]'
    ];

    protected $validationMessages = [];
    protected $skipValidation     = false;

    // Hash password before saving
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving to database
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password']) && !empty($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Authenticate dakoii user for login
     */
    public function authenticateDakoiiUser(string $username, string $password)
    {
        $user = $this->where('username', $username)->first();

        if (!$user) {
            return false;
        }

        if (!$user['is_active']) {
            return false;
        }

        if (!password_verify($password, $user['password'])) {
            return false;
        }

        // Remove password from returned data for security
        unset($user['password']);
        return $user;
    }

    /**
     * Get all active dakoii users
     */
    public function getActiveDakoiiUsers()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get dakoii users by role
     */
    public function getDakoiiUsersByRole(string $role)
    {
        return $this->where('role', $role)->findAll();
    }

    /**
     * Check if username already exists in dakoii_users
     */
    public function dakoiiUsernameExists(string $username, int $excludeId = null): bool
    {
        $builder = $this->where('username', $username);
        
        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() > 0;
    }

    /**
     * Get dakoii user statistics for dashboard
     */
    public function getDakoiiUserStats(): array
    {
        return [
            'total_dakoii_users'     => $this->countAllResults(),
            'active_dakoii_users'    => $this->where('is_active', 1)->countAllResults(),
            'inactive_dakoii_users'  => $this->where('is_active', 0)->countAllResults(),
            'admin_dakoii_users'     => $this->where('role', 'admin')->countAllResults(),
            'moderator_dakoii_users' => $this->where('role', 'moderator')->countAllResults(),
            'user_dakoii_users'      => $this->where('role', 'user')->countAllResults()
        ];
    }

    /**
     * Get dakoii user without password field
     */
    public function getSafeDakoiiUser(int $userId)
    {
        $user = $this->find($userId);
        if ($user) {
            unset($user['password']);
        }
        return $user;
    }

    /**
     * Create new dakoii user
     */
    public function createDakoiiUser(array $userData)
    {
        // Check if username already exists
        if ($this->dakoiiUsernameExists($userData['username'])) {
            return false;
        }

        // Set default active status if not provided
        if (!isset($userData['is_active'])) {
            $userData['is_active'] = 1;
        }

        return $this->insert($userData);
    }

    /**
     * Update dakoii user
     */
    public function updateDakoiiUser(int $userId, array $userData)
    {
        // Remove empty password to avoid updating it
        if (isset($userData['password']) && empty($userData['password'])) {
            unset($userData['password']);
        }

        // Check username uniqueness if being updated
        if (isset($userData['username']) && $this->dakoiiUsernameExists($userData['username'], $userId)) {
            return false;
        }

        return $this->update($userId, $userData);
    }

    /**
     * Deactivate dakoii user
     */
    public function deactivateDakoiiUser(int $userId): bool
    {
        return $this->update($userId, ['is_active' => 0]);
    }

    /**
     * Activate dakoii user
     */
    public function activateDakoiiUser(int $userId): bool
    {
        return $this->update($userId, ['is_active' => 1]);
    }
}
